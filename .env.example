# Zendesk Configuration
ZENDESK_DOMAIN=your-subdomain
ZENDESK_EMAIL=<EMAIL>
ZENDESK_TOKEN=your-zendesk-api-token

# ClickUp Configuration  
# Option 1: Direct API Token (works for personal workspaces)
CLICKUP_TOKEN=your-clickup-api-token

# Option 2: OAuth App (recommended for company/team workspaces)
CLICKUP_CLIENT_ID=your-clickup-oauth-client-id
CLICKUP_CLIENT_SECRET=your-clickup-oauth-client-secret
CLICKUP_REDIRECT_URI=https://your-worker-domain.workers.dev/auth/clickup/callback

# Required: Target list for creating tasks
CL<PERSON><PERSON>UP_LIST_ID=your-list-id

# Optional: Specific team/space IDs (usually auto-detected via OAuth)
CLICKUP_TEAM_ID=your-team-id
CLICKUP_SPACE_ID=your-space-id

# Slack Configuration (TaskGenie Bot)
SLACK_BOT_TOKEN=xoxb-your-bot-token
SLACK_SIGNING_SECRET=your-signing-secret
SLACK_APP_TOKEN=xapp-your-app-token

# AI Provider Configuration
# Choose one: googlegemini, openai, openrouter
AI_PROVIDER=googlegemini

# Google Gemini (recommended for getting started)
GOOGLE_GEMINI_API_KEY=your-gemini-api-key

# OpenAI (future implementation)
# OPENAI_API_KEY=your-openai-api-key

# OpenRouter (future implementation)
# OPENROUTER_API_KEY=your-openrouter-api-key
# OPENROUTER_MODEL=meta-llama/llama-3.1-8b-instruct:free

# Security & Development
WEBHOOK_SECRET=your-webhook-secret
ENVIRONMENT=development