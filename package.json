{"name": "cloudflare-workers-openapi", "version": "0.0.1", "private": true, "scripts": {"deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --config wrangler.dev.jsonc", "deploy:prod": "wrangler deploy --config wrangler.production.jsonc", "dev": "wrangler dev", "dev:prod": "wrangler dev --config wrangler.production.jsonc", "start": "wrangler dev", "cf-typegen": "wrangler types"}, "dependencies": {"@google/generative-ai": "^0.7.1", "chanfana": "^2.6.3", "hono": "^4.6.20", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "22.13.0", "@types/service-worker-mock": "^2.0.4", "typescript": "^5.9.2", "wrangler": "^4.28.0"}}