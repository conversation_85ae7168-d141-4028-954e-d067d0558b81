{"name": "cloudflare-workers-openapi", "version": "0.0.1", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "cf-typegen": "wrangler types"}, "dependencies": {"@google/generative-ai": "^0.7.1", "chanfana": "^2.6.3", "hono": "^4.6.20", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "22.13.0", "@types/service-worker-mock": "^2.0.4", "typescript": "^5.9.2", "wrangler": "^4.28.0"}}